import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import requests
import json
import os
import sys
import hashlib
import winreg
from pathlib import Path
import time

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from keyauth import api

class SteamToolsGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Steam Tools Downloader v2")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.steam_path = ""
        self.keyauth_app = None
        self.license_key = ""
        self.app_info = {}

        # Create GUI first
        self.create_widgets()

        # Initialize Keyauth after GUI is created
        self.init_keyauth()

        # Auto-detect Steam path
        self.auto_detect_steam_path()
    
    def init_keyauth(self):
        """Initialize Keyauth API"""
        try:
            self.keyauth_app = api(
                name="MainSteam",
                ownerid="1tGVnUKtzH",
                secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                version="1.0",
                hash_to_check=self.get_checksum()
            )
            self.log_message("Keyauth initialized successfully")
        except Exception as e:
            self.log_message(f"Failed to initialize Keyauth: {str(e)}")
            self.log_message("Please check your internet connection and try again.")
            # Don't exit, allow user to retry later
    
    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""
    
    def create_widgets(self):
        """Create the GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Steam Tools Downloader v2", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # License Key Section
        ttk.Label(main_frame, text="License Key:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.license_entry = ttk.Entry(main_frame, width=40)
        self.license_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        validate_frame = ttk.Frame(main_frame)
        validate_frame.grid(row=1, column=2, pady=5, padx=(5, 0))

        self.validate_btn = ttk.Button(validate_frame, text="Validate", command=self.validate_license)
        self.validate_btn.pack(side=tk.LEFT)

        self.retry_keyauth_btn = ttk.Button(validate_frame, text="Retry Keyauth", command=self.init_keyauth)
        self.retry_keyauth_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Steam Path Section
        ttk.Label(main_frame, text="Steam Path:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.steam_path_var = tk.StringVar()
        self.steam_path_entry = ttk.Entry(main_frame, textvariable=self.steam_path_var, width=40)
        self.steam_path_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.browse_btn = ttk.Button(main_frame, text="Browse", command=self.browse_steam_path)
        self.browse_btn.grid(row=2, column=2, pady=5, padx=(5, 0))
        
        # App Info Section
        ttk.Label(main_frame, text="App Information:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        self.app_info_text = tk.Text(main_frame, height=4, width=50)
        self.app_info_text.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # Progress Section
        ttk.Label(main_frame, text="Download Progress:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.progress_var = tk.StringVar(value="Ready")
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=4, column=1, sticky=tk.W, pady=5, padx=(5, 0))
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # Download Button
        self.download_btn = ttk.Button(main_frame, text="Download Files", 
                                     command=self.start_download, state='disabled')
        self.download_btn.grid(row=6, column=0, columnspan=3, pady=20)
        
        # Status Log
        ttk.Label(main_frame, text="Status Log:").grid(row=7, column=0, sticky=(tk.W, tk.N), pady=5)
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configure row weights for resizing
        main_frame.rowconfigure(8, weight=1)
    
    def log_message(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path"""
        try:
            reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Valve\\Steam")
            steam_path = winreg.QueryValueEx(reg_key, "SteamPath")[0]
            winreg.CloseKey(reg_key)
            
            if steam_path and os.path.exists(steam_path):
                self.steam_path = steam_path
                self.steam_path_var.set(steam_path)
                self.log_message(f"Steam path auto-detected: {steam_path}")
            else:
                self.log_message("Steam path found in registry but directory doesn't exist")
        except Exception:
            self.log_message("Steam path not found in registry. Please select manually.")
    
    def browse_steam_path(self):
        """Browse for Steam installation path"""
        path = filedialog.askdirectory(title="Select Steam Installation Directory")
        if path:
            if self.validate_steam_path(path):
                self.steam_path = path
                self.steam_path_var.set(path)
                self.log_message(f"Steam path set to: {path}")
            else:
                messagebox.showerror("Invalid Path", "Selected directory is not a valid Steam installation.")
    
    def validate_steam_path(self, path):
        """Validate if the path is a valid Steam installation"""
        steam_exe = os.path.join(path, "steam.exe")
        return os.path.exists(steam_exe)
    
    def validate_license(self):
        """Validate license key with Keyauth"""
        self.license_key = self.license_entry.get().strip()
        if not self.license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return
        
        self.log_message("Validating license key...")
        
        # Run validation in separate thread to prevent GUI freezing
        threading.Thread(target=self._validate_license_thread, daemon=True).start()
    
    def _validate_license_thread(self):
        """License validation thread"""
        try:
            # Validate license with Keyauth
            is_valid = self.keyauth_app.license(self.license_key)
            
            if is_valid:
                self.log_message("License key validated successfully")
                
                # Get app information
                main_data = self.keyauth_app.var("Main")
                if main_data:
                    apps_data = json.loads(main_data)
                    
                    # Find matching app based on license prefix
                    for app in apps_data["apps"]:
                        if self.license_key.startswith(app["license_prefix"]):
                            self.app_info = app
                            break
                    
                    if self.app_info:
                        # Update GUI in main thread
                        self.root.after(0, self._update_app_info)
                    else:
                        self.root.after(0, lambda: self.log_message("No matching app found for license prefix"))
                else:
                    self.root.after(0, lambda: self.log_message("Failed to get app data from Keyauth"))
            else:
                self.root.after(0, lambda: self.log_message("Invalid license key"))
                
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"License validation error: {str(e)}"))
    
    def _update_app_info(self):
        """Update app info display"""
        info_text = f"App Name: {self.app_info['app_name']}\n"
        info_text += f"App ID: {self.app_info['app_id']}\n"
        info_text += f"License Prefix: {self.app_info['license_prefix']}"
        
        self.app_info_text.delete(1.0, tk.END)
        self.app_info_text.insert(1.0, info_text)
        
        # Enable download button if Steam path is valid
        if self.steam_path and self.validate_steam_path(self.steam_path):
            self.download_btn.config(state='normal')
        
        self.log_message(f"App information loaded: {self.app_info['app_name']}")

    def start_download(self):
        """Start the download process"""
        if not self.steam_path or not self.validate_steam_path(self.steam_path):
            messagebox.showerror("Error", "Please select a valid Steam installation path")
            return

        if not self.app_info:
            messagebox.showerror("Error", "Please validate your license key first")
            return

        self.download_btn.config(state='disabled')
        self.progress_bar['value'] = 0
        self.progress_var.set("Starting download...")

        # Run download in separate thread
        threading.Thread(target=self._download_thread, daemon=True).start()

    def _download_thread(self):
        """Download files thread"""
        try:
            total_files = 4  # hid.dll, luapacka.exe, Steamtools.st, app.lua
            current_file = 0

            # Create stplug-in directory if it doesn't exist
            stplug_in_path = os.path.join(self.steam_path, "config", "stplug-in")
            os.makedirs(stplug_in_path, exist_ok=True)
            self.root.after(0, lambda: self.log_message(f"Created/verified directory: {stplug_in_path}"))

            # Download hid.dll to Steam root
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading hid.dll ({current_file}/{total_files})"))
            steamtools_url = self.keyauth_app.var("SteamTools")
            if steamtools_url:
                hid_path = os.path.join(self.steam_path, "hid.dll")
                if self.download_file(steamtools_url, hid_path):
                    self.root.after(0, lambda: self.log_message("✓ hid.dll downloaded successfully"))
                else:
                    self.root.after(0, lambda: self.log_message("✗ Failed to download hid.dll"))

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download SteamTools-File1 (luapacka.exe)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading luapacka.exe ({current_file}/{total_files})"))
            file1_url = self.keyauth_app.var("SteamTools-File1")
            if file1_url:
                file1_path = os.path.join(stplug_in_path, "luapacka.exe")
                if self.download_file(file1_url, file1_path):
                    self.root.after(0, lambda: self.log_message("✓ luapacka.exe downloaded successfully"))
                else:
                    self.root.after(0, lambda: self.log_message("✗ Failed to download luapacka.exe"))

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download SteamTools-File2 (Steamtools.st)
            current_file += 1
            self.root.after(0, lambda: self.progress_var.set(f"Downloading Steamtools.st ({current_file}/{total_files})"))
            file2_url = self.keyauth_app.var("SteamTools-File2")
            if file2_url:
                file2_path = os.path.join(stplug_in_path, "Steamtools.st")
                if self.download_file(file2_url, file2_path):
                    self.root.after(0, lambda: self.log_message("✓ Steamtools.st downloaded successfully"))
                else:
                    self.root.after(0, lambda: self.log_message("✗ Failed to download Steamtools.st"))

            self.root.after(0, lambda: self.progress_bar.config(value=(current_file/total_files)*100))

            # Download app-specific lua file
            current_file += 1
            app_name = self.app_info['app_name']
            self.root.after(0, lambda: self.progress_var.set(f"Downloading {app_name}.lua ({current_file}/{total_files})"))
            app_url = self.keyauth_app.var(app_name)
            if app_url:
                lua_filename = f"{self.app_info['app_id']}.lua"
                lua_path = os.path.join(stplug_in_path, lua_filename)
                if self.download_file(app_url, lua_path):
                    self.root.after(0, lambda: self.log_message(f"✓ {lua_filename} downloaded successfully"))
                else:
                    self.root.after(0, lambda: self.log_message(f"✗ Failed to download {lua_filename}"))

            self.root.after(0, lambda: self.progress_bar.config(value=100))
            self.root.after(0, lambda: self.progress_var.set("Download completed!"))
            self.root.after(0, lambda: self.log_message("All downloads completed!"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"Download error: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.download_btn.config(state='normal'))

    def download_file(self, url, file_path):
        """Download a file from URL to local path"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            return True
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"Download error for {os.path.basename(file_path)}: {str(e)}"))
            return False

def main():
    root = tk.Tk()
    app = SteamToolsGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
