from cryptography.fernet import <PERSON><PERSON><PERSON>


# Function to generate and save a key
def generate_key():
    key = Fernet.generate_key()
    with open("../secret.key", "wb") as key_file:
        key_file.write(key)
    print("Key generated and saved to '../secret.key'")


# Function to load the key from a file
def load_key():
    return open("../secret.key", "rb").read()


# Function to encrypt a message
def encrypt_message(message):
    key = load_key()
    f = Fernet(key)
    encrypted_message = f.encrypt(message.encode())
    return encrypted_message


# Function to decrypt a message
def decrypt_message(encrypted_message):
    key = load_key()
    f = Fernet(key)
    decrypted_message = f.decrypt(encrypted_message).decode()
    return decrypted_message


if __name__ == "__main__":
    encrypt_message = encrypt_message("EZPZ")

    print("Encrypted Message:", encrypt_message)
    print("Decrypted Message:", decrypt_message(encrypt_message))

