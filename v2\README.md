# Steam Tools Downloader v2

A GUI application for downloading Steam tools and game-specific files based on Keyauth license validation.

## Features

- **License Validation**: Validates license keys with Keyauth system
- **Auto Steam Detection**: Automatically detects Steam installation path from Windows registry
- **Manual Path Selection**: Browse and select Steam path if auto-detection fails
- **File Downloads**: Downloads required files to appropriate Steam directories:
  - `hid.dll` → Steam root folder
  - `luapacka.exe` → Steam/config/stplug-in/
  - `Steamtools.st` → Steam/config/stplug-in/
  - `{app_id}.lua` → Steam/config/stplug-in/
- **Progress Tracking**: Real-time download progress and status updates
- **Error Handling**: Comprehensive error handling and user feedback

## Requirements

- Python 3.7+
- Windows OS (for Steam registry detection)
- Valid Keyauth license key

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python steam_tools_gui.py
```

## Usage

1. **Enter License Key**: Input your license key in the format `PREFIX-XXXXXXXX`
2. **Validate License**: Click "Validate" to authenticate with Keyauth
3. **Verify Steam Path**: The application will auto-detect Steam path, or browse manually
4. **Download Files**: Click "Download Files" to start the download process
5. **Monitor Progress**: Watch the progress bar and status log for updates

## Supported License Prefixes

The application supports various game license prefixes as configured in the Keyauth system:
- `CODMW2-` for Call of Duty: Modern Warfare 2
- `BANANA-` for Banana game
- `GHOST-` for Ghost game
- `ELDEN-` for Elden Ring
- `WUKONG-` for Black Myth: Wukong

## File Structure

```
v2/
├── steam_tools_gui.py    # Main GUI application
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Troubleshooting

- **Steam Path Not Found**: Manually browse and select your Steam installation directory
- **License Validation Failed**: Ensure your license key is valid and has the correct prefix
- **Download Errors**: Check your internet connection and firewall settings
- **Permission Errors**: Run the application as administrator if needed

## Notes

- The application creates the `stplug-in` folder automatically if it doesn't exist
- All downloads are performed with progress tracking and error handling
- The GUI remains responsive during downloads using background threading
