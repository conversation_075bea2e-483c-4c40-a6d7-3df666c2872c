# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Tools
Use the tool launcher for easy access to all components:
```cmd
run_tools.cmd
```

Individual tools can be run directly:
```bash
# Main client tool (CLI)
cd src && python client_tool.py --auth

# GUI client tool
cd src && python client_tool_gui.py

# Mini client tool (background monitoring)
cd src && python mini_client_tool.py

# Manifest generator
cd src && python manifest_generator.py

# Server automation
cd src && python server_automation.py

# Test tool (OCR activation detection)
cd src && python test.py

# Encryption tool
cd src && python encryption_tool.py
```

### Building Executables
```bash
# Build main tool
pyinstaller config/MTYB_SteamTool.spec

# Build mini tool
pyinstaller config/MTYB_MiniSteamTool.spec

# Build test tool
pyinstaller config/MTYB_Test.spec

# Build all (via tool launcher option 8)
run_tools.cmd
```

### Dependencies
Install required packages:
```bash
pip install -r config/requirements.txt
```

## Architecture Overview

This is a Steam game manifest management system with license-based authentication. The core architecture consists of:

### Core Components
- **Authentication Layer**: KeyAuth-based licensing system (`keyauth.py`)
- **Steam Integration**: Manifest downloading and Steam API interaction (`manifest_generator.py`)
- **Client Tools**: Multiple interfaces for different use cases
  - CLI tool (`client_tool.py`) - Main interactive interface
  - GUI tool (`client_tool_gui.py`) - Windows-based interface
  - Mini tool (`mini_client_tool.py`) - Background monitoring
- **Server Component**: Flask-based automation server (`server_automation.py`)
- **Security**: Encryption utilities and secure data handling (`encryption_tool.py`)

### Data Flow
1. License authentication through KeyAuth API
2. Steam path detection via Windows registry
3. Manifest downloading from Steam servers
4. Local caching in `depots/` directory with VDF configuration
5. Server automation for batch operations

### Key Files
- `secret.key`: Encryption key (keep secure)
- `config/requirements.txt`: Python dependencies
- `depots/*/config.vdf`: Steam depot configurations
- `cache/app.log`: Application logging

### Windows Integration
- Registry access for Steam path detection
- Windows security API integration
- Firewall and antivirus management
- OCR-based Steam activation detection using `assets/steam_activate_product.png`

## Project Structure
- `src/`: All Python source code
- `config/`: Configuration files and PyInstaller specs
- `scripts/`: PowerShell and batch automation scripts
- `assets/`: Icons and image templates
- `depots/`: Steam game manifest cache
- `cache/`: Temporary files and logs