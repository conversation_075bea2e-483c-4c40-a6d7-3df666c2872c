@echo off
echo ========================================
echo Steam Manifest Updater - Tool Launcher
echo ========================================
echo.
echo Available tools:
echo 1. Main Client Tool (CLI)
echo 2. GUI Client Tool
echo 3. Mini Client Tool (Background)
echo 4. Manifest Generator
echo 5. Server Automation
echo 6. Test Tool (OCR)
echo 7. Encryption Tool
echo 8. Build Tools (PyInstaller)
echo 9. Exit
echo.
set /p choice="Select a tool (1-9): "

if "%choice%"=="1" (
    echo Starting Main Client Tool...
    cd src
    python client_tool.py --auth
    cd ..
) else if "%choice%"=="2" (
    echo Starting GUI Client Tool...
    cd src
    python client_tool_gui.py
    cd ..
) else if "%choice%"=="3" (
    echo Starting Mini Client Tool...
    cd src
    python mini_client_tool.py
    cd ..
) else if "%choice%"=="4" (
    echo Starting Manifest Generator...
    cd src
    python manifest_generator.py
    cd ..
) else if "%choice%"=="5" (
    echo Starting Server Automation...
    cd src
    python server_automation.py
    cd ..
) else if "%choice%"=="6" (
    echo Starting Test Tool...
    cd src
    python test.py
    cd ..
) else if "%choice%"=="7" (
    echo Starting Encryption Tool...
    cd src
    python encryption_tool.py
    cd ..
) else if "%choice%"=="8" (
    echo Building executables...
    pyinstaller config\MTYB_SteamTool.spec
    pyinstaller config\MTYB_MiniSteamTool.spec
    echo Build complete! Check dist\ folder for executables.
) else if "%choice%"=="9" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Please try again.
    pause
    goto :eof
)

pause
