@echo off
title Steam Tools Downloader v2 - Setup and Run
echo ========================================
echo Steam Tools Downloader v2 - Setup
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo WARNING: Some dependencies may have failed to install.
    echo The application may still work if dependencies are already installed.
    echo.
)

echo.
echo Starting Steam Tools Downloader v2...
echo.
python steam_tools_gui.py

echo.
echo Application closed.
pause
